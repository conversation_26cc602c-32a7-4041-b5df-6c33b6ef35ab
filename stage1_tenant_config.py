#!/usr/bin/env python3.12
"""Tenant configuration setup script for LEOS360 platform."""

import os
import sys
import re
import shutil
import subprocess
import argparse
import secrets
import base64
import datetime
import time
from pathlib import Path
from typing import Dict, Set, Optional, Tuple


class TenantConfigSetup:
    """Handles configuration setup for a new tenant."""
    
    def __init__(self, customer_name: str) -> None:
        """Initialize tenant configuration.
        
        Args:
            customer_name: Name of the customer/tenant (lowercase alphanumeric with hyphens)
        """
        self.customer_name = customer_name

        # Base configuration
        self.base_domain = "leos360.cloud"
        self.master_base = Path("/mnt/storage/setup")
        self.ssl_base = self.master_base / "ssl"
        self.keycloak_realm = f"{customer_name}.{self.base_domain}"

        # Customer specific configuration
        self.customer_domain = f"{customer_name}.{self.base_domain}"
        self.config_base = Path(f"/mnt/storage/tenants/{customer_name}")

        # Database configuration
        self.external_db_host = "************"
        self.external_db_port = "5432"

        # Service paths
        self.dovecot_config = self.config_base / "dovecot"
        self.lldap_config = self.config_base / "lldap"
        self.keycloak_config = self.config_base / "keycloak"
        self.postfix_config = self.config_base / "postfix"
        self.db_config = self.config_base / "db"
        self.redis_config = self.config_base / "redis"
        self.nextcloud_config = self.config_base / "nextcloud"

        # Customer IP and Redis DB Index
        self.customer_ip = ""
        self.redis_db_index = ""
        self.redis_secret = ""

        # Signal secret
        self.signal_secret = ""

        # Password variables
        self.db_password = ""
        self.nextcloud_admin_password = ""
        self.keycloak_admin_password = ""
        self.lldap_jwt_secret = ""
        self.lldap_key_seed = ""
        self.lldap_admin_pass = ""
        self.lldap_ro_pass = ""
        self.nextcloud_keycloak_client_secret = ""
        self.leos360portal_keycloak_client_secret = ""

    # Helper functions for common operations
    def replace_variables(self, content: str, replacements: Dict[str, str]) -> str:
        """Replace variables in content with their values.
        
        Args:
            content: String containing variables to replace
            replacements: Dictionary of placeholder-value pairs
            
        Returns:
            Content with all placeholders replaced
        """
        for placeholder, value in replacements.items():
            if placeholder.startswith("${") and placeholder.endswith("}"):
                content = content.replace(placeholder, value)
        return content

    def copy_and_set_permissions(self, source: Path, target: Path, 
                               permissions: int = 0o644) -> bool:
        """Copy file and set permissions.
        
        Args:
            source: Source file path
            target: Destination file path
            permissions: File permissions (default: 0o644)
            
        Returns:
            True if successful, False otherwise
        """
        if not source.exists():
            print(f"Warning: Source file {source} not found")
            return False
            
        target.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy(source, target)
        os.chmod(target, permissions)
        return True

    def ensure_directory(self, directory: Path, permissions: int = 0o755) -> Path:
        """Ensure directory exists with proper permissions.
        
        Args:
            directory: Directory path
            permissions: Directory permissions (default: 0o755)
            
        Returns:
            Path to the created directory
        """
        directory.mkdir(parents=True, exist_ok=True)
        os.chmod(directory, permissions)
        return directory

    def _find_tenant_dirs(self, search_path: str) -> Set[str]:
        """Find all tenant directories.
        
        Args:
            search_path: Path to search for tenant directories
            
        Returns:
            Set of tenant directory paths
        """
        try:
            return {d for d in os.listdir(search_path) 
                   if os.path.isdir(os.path.join(search_path, d))}
        except Exception as e:
            print(f"Error listing directories: {e}")
            return set()

    def _get_used_ips_and_indices(self, tenant_dirs: Set[str]) -> Tuple[Set[str], Set[int]]:
        """Get used IPs and indices from tenant directories.
        
        Args:
            tenant_dirs: Set of tenant directory paths
            
        Returns:
            Tuple of (used_ips, used_indices)
        """
        used_ips = set()
        used_indices = set()
        
        for tenant_dir in tenant_dirs:
            env_file = Path(tenant_dir) / ".env"
            if env_file.exists():
                try:
                    content = env_file.read_text()
                    if "CUSTOMER_IP=" in content:
                        ip = content.split("CUSTOMER_IP=")[1].split("\n")[0].strip()
                        if ip:
                            used_ips.add(ip)
                            try:
                                used_indices.add(int(ip.split('.')[-1]))
                            except ValueError:
                                pass
                    
                    if "REDIS_DB_INDEX=" in content:
                        db_index = content.split("REDIS_DB_INDEX=")[1].split("\n")[0].strip()
                        if db_index:
                            try:
                                used_indices.add(int(db_index))
                            except ValueError:
                                pass
                except Exception as e:
                    print(f"Warning: Could not process {env_file}: {e}")
                    
        return used_ips, used_indices

    def setup_nextfreeip(self) -> None:
        """Get the next free IP address and Redis DB index for the customer."""
        print("Getting next free IP address and Redis DB index...")
        
        # Configuration
        SEARCH_PATH = "/mnt/storage/tenants/"
        IP_START = 20   # Last octet of ***********
        IP_END = 150    # Last octet of ************
        IP_PREFIX = "172.16.7."
        
        # Find used resources
        tenant_dirs = self._find_tenant_dirs(SEARCH_PATH)
        used_ips, used_indices = self._get_used_ips_and_indices(tenant_dirs)
        
        # Find next available
        for i in range(IP_START, IP_END + 1):
            if i not in used_indices:
                self.customer_ip = f"{IP_PREFIX}{i}"
                self.redis_db_index = str(i)
                return
                
        print("Error: No available IP addresses or Redis DB indices found")
        sys.exit(1)

    def check_prerequisites(self):
        """Validate prerequisites and requirements"""
        # Check if running as root
        if os.geteuid() != 0:
            print("This script must be run as root")
            sys.exit(1)

        # Check required directories
        if not self.master_base.exists():
            print(f"Error: Master directory {self.master_base} does not exist")
            sys.exit(1)

        if not self.ssl_base.exists():
            print(f"Error: SSL directory {self.ssl_base} does not exist")
            sys.exit(1)

        # Check if customer directory already exists
        if self.config_base.exists():
            print(f"Error: Customer directory {self.config_base} already exists")
            sys.exit(1)

        # Check required template files
        required_files = [
            self.master_base / ".env",
            self.master_base / "db" / "db_setup.sql.template",
            self.master_base / "dovecot" / "dovecot-ldap-userdb.conf",
            self.master_base / "dovecot" / "dovecot-ldap-passdb.conf",
            self.master_base / "postfix" / "ldap" / "virtual_aliases.cf",
            self.master_base / "postfix" / "ldap" / "virtual_domains.cf",
        ]

        for file in required_files:
            if not file.exists():
                print(f"Error: Required template file {file} not found")
                sys.exit(1)

    def generate_secure_password(self, length=32):
        """Generate a secure random password"""
        # Generate random bytes
        random_bytes = secrets.token_bytes(length)
        # Convert to base64 and remove potentially problematic characters
        password = base64.b64encode(random_bytes).decode('utf-8')
        # Trim to specified length and remove problematic characters
        password = re.sub(r'[=+/]', '', password)[:length]
        return password

    def generate_passwords(self):
        """Generate secure passwords and save to credentials file"""
        print("Generating secure passwords...")

        # Generate passwords
        self.db_password = self.generate_secure_password()
        self.nextcloud_admin_password = self.generate_secure_password()
        self.keycloak_admin_password = self.generate_secure_password()
        self.lldap_jwt_secret = self.generate_secure_password()
        self.lldap_key_seed = self.generate_secure_password()
        self.lldap_admin_pass = self.generate_secure_password()
        self.lldap_ro_pass = self.generate_secure_password()
        self.signal_secret = self.generate_secure_password()
        self.redis_secret = self.generate_secure_password()
        self.nextcloud_keycloak_client_secret = self.generate_secure_password()
        self.leos360portal_keycloak_client_secret = self.generate_secure_password()

        # Create .secrets directory
        secrets_dir = self.config_base / ".secrets"
        self.ensure_directory(secrets_dir, 0o750)

        # Save passwords to credentials file
        credentials_content = f"""# Generated credentials for {self.customer_name}
# Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Customer IP: {self.customer_ip}
Database Password: {self.db_password}
Nextcloud Admin Password: {self.nextcloud_admin_password}
Keycloak Admin Password: {self.keycloak_admin_password}
LLDAP JWT Secret: {self.lldap_jwt_secret}
LLDAP Key Seed: {self.lldap_key_seed}
LLDAP Admin Password: {self.lldap_admin_pass}
LLDAP RO Password: {self.lldap_ro_pass}
Signal Secret: {self.signal_secret}
Redis DB Index: {self.redis_db_index}
Redis Password: {self.redis_secret}
Nextcloud Keycloak Client Secret: {self.nextcloud_keycloak_client_secret}
Leos360 Portal Keycloak Client Secret: {self.leos360portal_keycloak_client_secret}
"""

        credentials_file = secrets_dir / "credentials.txt"
        credentials_file.write_text(credentials_content)
        os.chmod(credentials_file, 0o640)

    def create_directory_structure(self):
        """Create directory structure for tenant"""
        print(f"Creating directory structure for {self.customer_name}...")

        # Base directories
        for dir_name in ["nextcloud", "keycloak", "lldap", "dovecot", "postfix", "db", "redis"]:
            self.ensure_directory(self.config_base / dir_name)
            
        # Redis structure
        self.ensure_directory(self.config_base / "redis" / "data")

        # Nextcloud structure
        for subdir in ["data", "config", "custom_apps", "templates", "setup", "html"]:
            self.ensure_directory(self.config_base / "nextcloud" / subdir)

        # Keycloak structure
        for subdir in ["data", "config"]:
            self.ensure_directory(self.config_base / "keycloak" / subdir)

        # LLDAP structure
        lldap_dirs = [
            "data",
            "config/bootstrap/group-configs",
            "config/bootstrap/group-schemas",
            "config/bootstrap/user-configs",
            "config/bootstrap/user-schemas"
        ]
        for subdir in lldap_dirs:
            self.ensure_directory(self.config_base / "lldap" / subdir)

        # Dovecot structure
        dovecot_dirs = ["data/mail", "config/conf.d", "config/ldap", "config/ssl", "config/scripts"]
        for subdir in dovecot_dirs:
            self.ensure_directory(self.config_base / "dovecot" / subdir)

        # Postfix structure
        postfix_dirs = ["config/ldap", "config/main.cf.d", "data/spool"]
        for subdir in postfix_dirs:
            self.ensure_directory(self.config_base / "postfix" / subdir)

        # Set permissions
        os.chmod(self.config_base, 0o755)

    def create_env_file(self):
        """Create .env file with proper substitutions"""
        print("Creating .env file...")

        # Copy master .env template
        env_file = self.config_base / ".env"
        self.copy_and_set_permissions(self.master_base / ".env", env_file)

        # Read the content
        env_content = env_file.read_text()

        # Perform replacements
        replacements = {
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${CONFIG_BASE}": str(self.config_base),
            "${DB_PASSWORD}": self.db_password,
            "${NEXTCLOUD_ADMIN_PASSWORD}": self.nextcloud_admin_password,
            "${KEYCLOAK_ADMIN_PASSWORD}": self.keycloak_admin_password,
            "${LLDAP_JWT_SECRET}": self.lldap_jwt_secret,
            "${LLDAP_KEY_SEED}": self.lldap_key_seed,
            "${LLDAP_ADMIN_PASS}": self.lldap_admin_pass,
            "${LLDAP_RO_PASS}": self.lldap_ro_pass,
            "${EXTERNAL_DB_HOST}": self.external_db_host,
            "${EXTERNAL_DB_PORT}": self.external_db_port,
            "${EXTERNAL_DB_USER}": f"{self.customer_name}_admin",
            "${EXTERNAL_NEXTCLOUD_DB}": f"{self.customer_name}_nextcloud",
            "${EXTERNAL_KEYCLOAK_DB}": f"{self.customer_name}_keycloak",
            "${EXTERNAL_LLDAP_DB}": f"{self.customer_name}_lldap",
            "${CUSTOMER_IP}": self.customer_ip,
            "${SIGNAL_SECRET}": self.signal_secret,
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self.redis_secret,            
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self.nextcloud_keycloak_client_secret,
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self.leos360portal_keycloak_client_secret
        }

        # Replace variables
        env_content = self.replace_variables(env_content, replacements)

        # Write updated content
        env_file.write_text(env_content)

    def setup_ssl(self, ssl_dir):
        """Set up SSL certificates"""
        print("Setting up SSL certificates...")
        
        # Copy SSL certificates
        for file_path in self.ssl_base.glob("*"):
            self.copy_and_set_permissions(file_path, ssl_dir / file_path.name)

    def _setup_service_config(self, service_name: str, 
                            config_files: Dict[str, Tuple[Path, Path, Optional[int]]],
                            replacements: Optional[Dict[str, str]] = None) -> None:
        """Base method for setting up service configurations.
        
        Args:
            service_name: Name of the service being configured
            config_files: Dictionary of config files to process with:
                key: Description of file
                value: Tuple of (source_path, target_path, optional_permissions)
            replacements: Optional variable replacements
        """
        print(f"Setting up {service_name} configuration...")
        
        for desc, (source, target, permissions) in config_files.items():
            if not source.exists():
                print(f"Warning: {service_name} source file {source} not found")
                continue
                
            content = source.read_text()
            if replacements:
                content = self.replace_variables(content, replacements)
                
            target.parent.mkdir(parents=True, exist_ok=True)
            target.write_text(content)
            
            if permissions is not None:
                os.chmod(target, permissions)

    def setup_nextcloud(self) -> None:
        """Set up Nextcloud configuration."""
        replacements = {
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self.redis_secret,
            "${SIGNAL_SECRET}": self.signal_secret,
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${KEYCLOAK_REALM}": self.keycloak_realm,
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self.nextcloud_keycloak_client_secret,
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self.leos360portal_keycloak_client_secret
        }
        
        config_files = {
            "setup script": (
                self.master_base / "nextcloud" / "50-setup-nc.sh",
                self.config_base / "nextcloud" / "setup" / "50-setup-nc.sh",
                0o755
            )
        }
        
        self._setup_service_config("Nextcloud", config_files, replacements)

    def setup_keycloak(self) -> None:
        """Set up Keycloak configuration."""
        config_files = {
            "start script": (
                self.master_base / "keycloak" / "start-script.sh",
                self.config_base / "keycloak" / "start-script.sh",
                0o755
            ),
            "dockerfile": (
                self.master_base / "keycloak" / "dockerfile",
                self.config_base / "keycloak" / "dockerfile",
                None
            )
        }
        
        self._setup_service_config("Keycloak", config_files)

    def setup_dovecot(self):
        """Set up Dovecot configuration with proper variable replacement"""
        print("Setting up Dovecot configuration...")

        # Copy main Dovecot config files
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "dovecot.conf",
            self.config_base / "dovecot" / "config" / "dovecot.conf"
        )
        
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "scripts" / "quota-warning.sh",
            self.config_base / "dovecot" / "config" / "scripts" / "quota-warning.sh",
            0o755
        )
        
        # Copy conf.d files
        for file_path in (self.master_base / "dovecot" / "conf.d").glob("*"):
            self.copy_and_set_permissions(
                file_path,
                self.config_base / "dovecot" / "config" / "conf.d" / file_path.name
            )
        
        # Copy SSL files
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "ssl" / "dh.pem",
            self.config_base / "dovecot" / "config" / "ssl" / "dh.pem"
        )

        # Process LDAP configuration files
        config_files = [
            ("dovecot-ldap-passdb.conf", "ldap"),
            ("dovecot-ldap-userdb.conf", "ldap")
        ]

        for file_name, subdir in config_files:
            source_file = self.master_base / "dovecot" / file_name
            target_file = self.config_base / "dovecot" / "config" / subdir / file_name
            
            if source_file.exists():
                # Read content from source file
                content = source_file.read_text()
                
                # Replace variables
                replacements = {
                    "${CUSTOMER_NAME}": self.customer_name,
                    "${LLDAP_RO_PASS}": self.lldap_ro_pass,
                    "${CUSTOMER_IP}": self.customer_ip
                }
                
                content = self.replace_variables(content, replacements)
                
                # Ensure target directory exists
                (target_file.parent).mkdir(parents=True, exist_ok=True)
                
                # Write updated content to target file
                target_file.write_text(content)
                os.chmod(target_file, 0o644)
            else:
                print(f"Warning: Source file {source_file} not found")

    def setup_postfix(self):
        """Set up Postfix configuration with proper variable replacement"""
        print("Setting up Postfix configuration...")

        # Copy main configuration
        self.copy_and_set_permissions(
            self.master_base / "postfix" / "main.cf",
            self.config_base / "postfix" / "config" / "main.cf.d" / "main.cf"
        )

        # Process LDAP configuration files
        config_files = [
            "virtual_domains.cf",
            "virtual_aliases.cf"
        ]

        for file_name in config_files:
            source_file = self.master_base / "postfix" / "ldap" / file_name
            target_file = self.config_base / "postfix" / "config" / "ldap" / file_name
            
            if source_file.exists():
                # Read content from source file
                content = source_file.read_text()
                
                # Replace variables
                replacements = {
                    "${CUSTOMER_NAME}": self.customer_name,
                    "${LLDAP_RO_PASS}": self.lldap_ro_pass,
                    "${CUSTOMER_IP}": self.customer_ip
                }
                
                content = self.replace_variables(content, replacements)
                
                # Ensure target directory exists
                (target_file.parent).mkdir(parents=True, exist_ok=True)
                
                # Write updated content to target file
                target_file.write_text(content)
                os.chmod(target_file, 0o644)
            else:
                print(f"Warning: Source file {source_file} not found")

    def setup_database(self):
        """Set up database configuration"""
        print("Setting up database configuration...")

        db_setup_file = self.config_base / "db" / f"db_setup_{self.customer_name}.sql"
        self.ensure_directory(self.config_base / "db")

        template_file = self.master_base / "db" / "db_setup.sql.template"
        
        if template_file.exists():
            # Read the content
            sql_content = template_file.read_text()

            # Perform replacements
            replacements = {
                "${DB_ADMIN_USER}": f"{self.customer_name}_admin",
                "${DB_NEXTCLOUD}": f"{self.customer_name}_nextcloud",
                "${DB_KEYCLOAK}": f"{self.customer_name}_keycloak",
                "${DB_LLDAP}": f"{self.customer_name}_lldap",
                "${DB_PASSWORD}": self.db_password,
                "${CUSTOMER_IP}": self.customer_ip,
            }

            sql_content = self.replace_variables(sql_content, replacements)

            # Write updated content
            db_setup_file.write_text(sql_content)
            os.chmod(db_setup_file, 0o644)
        else:
            print(f"Warning: Database template file {template_file} not found")

    def setup_lldap(self):
        """Set up LLDAP configuration"""
        print("Setting up LLDAP configuration...")

        # Create directory structure
        lldap_bootstrap_dir = self.config_base / "lldap" / "config" / "bootstrap"
        
        # Create subdirectories
        for subdir in ["group-configs", "group-schemas", "user-configs", "user-schemas"]:
            self.ensure_directory(lldap_bootstrap_dir / subdir)

        # Copy files with proper source checks
        files_to_copy = [
            ("group-configs/groups.json", "group-configs/groups.json"),
            ("group-schemas/group-schemas.json", "group-schemas/group-schemas.json"),
            ("user-configs/users.json", "user-configs/users.json"),
            ("user-schemas/custom-attributes.json", "user-schemas/custom-attributes.json")
        ]
        
        for src_rel, dst_rel in files_to_copy:
            source = self.master_base / "lldap" / "bootstrap" / src_rel
            target = lldap_bootstrap_dir / dst_rel
            
            if source.exists():
                self.copy_and_set_permissions(source, target)
            else:
                print(f"Warning: LLDAP source file {source} not found")

        # Replace variables in the user configuration file
        user_config_file = lldap_bootstrap_dir / "user-configs" / "users.json"
        
        if user_config_file.exists():
            content = user_config_file.read_text()
            
            replacements = {
                "${CUSTOMER_NAME}": self.customer_name,
                "${LLDAP_ADMIN_PASS}": self.lldap_admin_pass,
                "${LLDAP_RO_PASS}": self.lldap_ro_pass,
                "${CUSTOMER_IP}": self.customer_ip
            }
            
            content = self.replace_variables(content, replacements)
            user_config_file.write_text(content)

    def setup_redis(self):
        """Set up Redis configuration"""
        print("Setting up Redis configuration...")
        
        # Create Redis data directory if not already created
        self.ensure_directory(self.config_base / "redis" / "data")

    def verify_setup(self):
        """Verify the setup"""
        print("Verifying Config Setup...")
        
        required_items = [
            self.config_base / ".env",
            self.config_base / "docker-compose.yml",
            self.config_base / "nextcloud",
            self.config_base / "keycloak",
            self.config_base / "lldap",
            self.config_base / "dovecot",
            self.config_base / "postfix"
        ]

        for item in required_items:
            if not item.exists():
                print(f"Error: Setup verification failed - {item} not found")
                sys.exit(1)
                
        print(f"Setup Config completed successfully for {self.customer_name}")

    def setup_services(self):
        """Coordinator function for setting up all services"""
        print("Setting up service configurations...")
        
        # Create common SSL directory
        ssl_dir = self.config_base / "ssl"
        self.ensure_directory(ssl_dir)
        self.setup_ssl(ssl_dir)
        
        # Setup individual services
        self.setup_nextcloud()
        self.setup_keycloak()
        self.setup_lldap()
        self.setup_dovecot()
        self.setup_postfix()
        self.setup_database()
        self.setup_redis()
        
        print("Service configurations setup completed")

    def cleanup(self):
        """Clean up in case of error"""
        if self.config_base.exists():
            print(f"Cleaning up {self.config_base} due to error...")
            shutil.rmtree(self.config_base, ignore_errors=True)

    def run(self):
        """Main execution function"""
        print(f"Starting tenant setup for {self.customer_name}...")

        try:
            self.check_prerequisites()

            # Create base directory
            self.config_base.mkdir(parents=True, exist_ok=True)
            self.setup_nextfreeip()
            self.generate_passwords()
            self.create_directory_structure()
            self.create_env_file()
            self.setup_services()
            
            # Copy docker-compose.yml
            self.copy_and_set_permissions(
                self.master_base / "docker-compose.yml", 
                self.config_base / "docker-compose.yml"
            )

            self.verify_setup()
            return True

        except Exception as e:
            print(f"Error during setup: {str(e)}")
            self.cleanup()
            return False


def main():
    """Main function to parse arguments and execute setup"""
    parser = argparse.ArgumentParser(description='Setup tenant configuration')
    parser.add_argument('customer_name', help='Name of the customer')

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Validate customer name
    if not re.match(r'^[a-z0-9-]+$', args.customer_name):
        print("Error: Customer name must contain only lowercase letters, numbers and hyphens")
        sys.exit(1)

    # Run setup
    setup = TenantConfigSetup(args.customer_name)
    success = setup.run()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()